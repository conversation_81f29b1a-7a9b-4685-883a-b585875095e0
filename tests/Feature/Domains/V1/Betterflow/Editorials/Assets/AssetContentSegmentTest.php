<?php

namespace Tests\Feature\Domains\V1\Betterflow\Editorials\Assets;

use App\Domains\Betterflow\V1\Editorials\Assets\Models\AssetContentSegment;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\Asset;
use App\Domains\Betterflow\V1\Editorials\Models\Assets\AssetContent;
use App\Domains\Betterflow\V1\Editorials\Models\Editorial;
use App\Domains\Users\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Plannr\Laravel\FastRefreshDatabase\Traits\FastRefreshDatabase;
use Tests\TestCase;

class AssetContentSegmentTest extends TestCase
{
    use FastRefreshDatabase;

    private User $user;
    private Editorial $editorial;
    private Asset $asset;
    private AssetContent $assetContent;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->editorial = Editorial::factory()->create();
        $this->asset = Asset::factory()->create([
            'editorial_id' => $this->editorial->id,
            'slug' => 'test-asset',
        ]);
        $this->assetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'en-EN',
        ]);
    }

    #[Test]
    public function it_can_create_a_new_segment(): void
    {
        $segmentData = [
            'title' => 'New Segment Title',
            'content' => 'This is the content of the new segment.',
            'metadata' => [
                'author' => 'Test Author',
                'created_by' => $this->user->name,
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'intro',
                'language' => 'en-EN',
                'data' => $segmentData,
            ]);

        $response->assertCreated()
            ->assertJsonStructure([
                'asset_id',
                'asset',
                'segment',
                'data',
            ])
            ->assertJsonPath('segment', 'intro')
            ->assertJsonPath('data.title', 'New Segment Title')
            ->assertJsonPath('data.content', 'This is the content of the new segment.')
            ->assertJsonPath('data.metadata.author', 'Test Author');

        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'intro',
        ]);
    }

    #[Test]
    public function it_can_create_segment_with_empty_data(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'empty-segment',
                'language' => 'en-EN',
                'data' => [],
            ]);

        $response->assertCreated()
            ->assertJsonPath('segment', 'empty-segment')
            ->assertJsonPath('data', []);

        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id,
            'segment' => 'empty-segment',
        ]);
    }

    #[Test]
    public function it_can_create_segment_with_complex_nested_data(): void
    {
        $complexData = [
            'title' => 'Complex Segment',
            'sections' => [
                [
                    'type' => 'text',
                    'content' => 'First section content',
                    'metadata' => ['position' => 1],
                ],
                [
                    'type' => 'image',
                    'url' => 'https://example.com/image.jpg',
                    'alt' => 'Example image',
                    'metadata' => ['position' => 2],
                ],
            ],
            'settings' => [
                'visible' => true,
                'priority' => 'high',
                'tags' => ['important', 'featured'],
            ],
        ];

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'complex-segment',
                'language' => 'en-EN',
                'data' => $complexData,
            ]);

        $response->assertCreated()
            ->assertJsonPath('segment', 'complex-segment')
            ->assertJsonPath('data.title', 'Complex Segment')
            ->assertJsonPath('data.sections.0.type', 'text')
            ->assertJsonPath('data.sections.1.type', 'image')
            ->assertJsonPath('data.settings.visible', true)
            ->assertJsonPath('data.settings.tags.0', 'important');
    }

    #[Test]
    public function it_can_create_segment_for_different_language(): void
    {
        // Create asset content for Spanish
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'spanish-intro',
                'language' => 'es-ES',
                'data' => [
                    'title' => 'Título en Español',
                    'content' => 'Contenido en español.',
                ],
            ]);

        $response->assertCreated()
            ->assertJsonPath('segment', 'spanish-intro')
            ->assertJsonPath('data.title', 'Título en Español');

        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $spanishAssetContent->id,
            'segment' => 'spanish-intro',
        ]);
    }

    #[Test]
    public function it_defaults_to_english_when_language_not_specified(): void
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'default-lang',
                'data' => ['title' => 'Default Language Title'],
            ]);

        $response->assertCreated()
            ->assertJsonPath('segment', 'default-lang');

        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $this->assetContent->id, // English content
            'segment' => 'default-lang',
        ]);
    }

    #[Test]
    public function it_cannot_create_duplicate_segment_for_same_asset_content(): void
    {
        // Create first segment
        AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('duplicate-segment')
            ->create();

        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'duplicate-segment',
                'language' => 'en-EN',
                'data' => ['title' => 'Duplicate Title'],
            ]);

        $response->assertStatus(422)
            ->assertJsonPath('message', 'Segment already exists');
    }

    #[Test]
    public function it_allows_same_segment_name_for_different_languages(): void
    {
        // Create Spanish asset content
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);

        // Create segment in English
        AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('intro')
            ->create();

        // Create same segment name in Spanish - should be allowed
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'intro',
                'language' => 'es-ES',
                'data' => ['title' => 'Introducción'],
            ]);

        $response->assertCreated()
            ->assertJsonPath('segment', 'intro')
            ->assertJsonPath('data.title', 'Introducción');

        $this->assertDatabaseHas('asset_content_segments', [
            'asset_content_id' => $spanishAssetContent->id,
            'segment' => 'intro',
        ]);
    }

    #[Test]
    public function it_can_retrieve_an_existing_segment(): void
    {
        $segmentData = [
            'title' => 'Existing Segment Title',
            'content' => 'This is existing content.',
            'metadata' => ['author' => 'Test Author'],
        ];

        $segment = AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('existing-segment')
            ->withData($segmentData)
            ->create();

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'existing-segment',
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'asset_id',
                'asset',
                'segment',
                'data',
            ])
            ->assertJsonPath('segment', 'existing-segment')
            ->assertJsonPath('data.title', 'Existing Segment Title')
            ->assertJsonPath('data.content', 'This is existing content.')
            ->assertJsonPath('data.metadata.author', 'Test Author');
    }

    #[Test]
    public function it_can_retrieve_segment_for_specific_language(): void
    {
        // Create Spanish asset content
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);

        $spanishSegment = AssetContentSegment::factory()
            ->forAssetContent($spanishAssetContent)
            ->withSegment('intro')
            ->withData([
                'title' => 'Título en Español',
                'content' => 'Contenido en español.',
            ])
            ->create();

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'intro',
                'language' => 'es-ES',
            ]));

        $response->assertOk()
            ->assertJsonPath('segment', 'intro')
            ->assertJsonPath('data.title', 'Título en Español')
            ->assertJsonPath('data.content', 'Contenido en español.');
    }

    #[Test]
    public function it_returns_404_when_segment_does_not_exist(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'non-existent-segment',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_can_update_an_existing_segment(): void
    {
        $originalData = [
            'title' => 'Original Title',
            'content' => 'Original content.',
        ];

        $segment = AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('updatable-segment')
            ->withData($originalData)
            ->create();

        $updatedData = [
            'title' => 'Updated Title',
            'content' => 'Updated content with new information.',
            'metadata' => ['updated_by' => $this->user->name],
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'updatable-segment',
            ]), [
                'data' => $updatedData,
            ]);

        $response->assertOk()
            ->assertJsonPath('segment', 'updatable-segment')
            ->assertJsonPath('data.title', 'Updated Title')
            ->assertJsonPath('data.content', 'Updated content with new information.')
            ->assertJsonPath('data.metadata.updated_by', $this->user->name);

        $segment->refresh();
        $this->assertEquals($updatedData, $segment->data);
    }

    #[Test]
    public function it_can_update_segment_for_specific_language(): void
    {
        // Create Spanish asset content
        $spanishAssetContent = AssetContent::factory()->create([
            'asset_id' => $this->asset->id,
            'language_code' => 'es-ES',
        ]);

        $segment = AssetContentSegment::factory()
            ->forAssetContent($spanishAssetContent)
            ->withSegment('intro')
            ->withData(['title' => 'Título Original'])
            ->create();

        $updatedData = [
            'title' => 'Título Actualizado',
            'content' => 'Contenido actualizado.',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'intro',
                'language' => 'es-ES',
            ]), [
                'data' => $updatedData,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Título Actualizado')
            ->assertJsonPath('data.content', 'Contenido actualizado.');

        $segment->refresh();
        $this->assertEquals($updatedData, $segment->data);
    }

    #[Test]
    public function it_can_update_segment_with_partial_data(): void
    {
        $originalData = [
            'title' => 'Original Title',
            'content' => 'Original content.',
            'metadata' => ['author' => 'Original Author'],
        ];

        $segment = AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('partial-update')
            ->withData($originalData)
            ->create();

        $partialUpdate = [
            'title' => 'Updated Title Only',
            'newField' => 'New field value',
        ];

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'partial-update',
            ]), [
                'data' => $partialUpdate,
            ]);

        $response->assertOk()
            ->assertJsonPath('data.title', 'Updated Title Only')
            ->assertJsonPath('data.newField', 'New field value');

        $segment->refresh();
        $this->assertEquals($partialUpdate, $segment->data);
    }

    #[Test]
    public function it_can_clear_segment_data_with_empty_update(): void
    {
        $segment = AssetContentSegment::factory()
            ->forAssetContent($this->assetContent)
            ->withSegment('clearable-segment')
            ->withData(['title' => 'Will be cleared'])
            ->create();

        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'clearable-segment',
            ]), [
                'data' => [],
            ]);

        $response->assertOk()
            ->assertJsonPath('data', []);

        $segment->refresh();
        $this->assertEquals([], $segment->data);
    }

    #[Test]
    public function it_returns_404_when_updating_non_existent_segment(): void
    {
        $response = $this->actingAs($this->user)
            ->putJson(route('betterflow.v1.editorials.assets.segment.update', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
                'segment' => 'non-existent-segment',
            ]), [
                'data' => ['title' => 'Updated Title'],
            ]);

        $response->assertNotFound();
    }

    #[Test]
    public function it_requires_authentication_for_all_endpoints(): void
    {
        // Test create endpoint
        $response = $this->postJson(route('betterflow.v1.editorials.assets.segments.store', [
            'editorial' => $this->editorial->getPublicKey(),
            'asset' => $this->asset->slug,
        ]), [
            'segment' => 'test',
            'data' => ['title' => 'Test'],
        ]);
        $response->assertUnauthorized();

        // Test show endpoint
        $response = $this->getJson(route('betterflow.v1.editorials.assets.segment', [
            'editorial' => $this->editorial->getPublicKey(),
            'asset' => $this->asset->slug,
            'segment' => 'test',
        ]));
        $response->assertUnauthorized();

        // Test update endpoint
        $response = $this->putJson(route('betterflow.v1.editorials.assets.segment.update', [
            'editorial' => $this->editorial->getPublicKey(),
            'asset' => $this->asset->slug,
            'segment' => 'test',
        ]), [
            'data' => ['title' => 'Updated'],
        ]);
        $response->assertUnauthorized();
    }

    #[Test]
    public function it_handles_invalid_editorial_public_key(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => 'invalid-uuid',
                'asset' => $this->asset->slug,
                'segment' => 'test',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_invalid_asset_slug(): void
    {
        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => 'invalid-slug',
                'segment' => 'test',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_asset_not_belonging_to_editorial(): void
    {
        $otherEditorial = Editorial::factory()->create();
        $otherAsset = Asset::factory()->create([
            'editorial_id' => $otherEditorial->id,
            'slug' => 'other-asset',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('betterflow.v1.editorials.assets.segment', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $otherAsset->slug,
                'segment' => 'test',
            ]));

        $response->assertNotFound();
    }

    #[Test]
    public function it_handles_missing_asset_content_for_language(): void
    {
        // Try to create segment for language that doesn't have asset content
        $response = $this->actingAs($this->user)
            ->postJson(route('betterflow.v1.editorials.assets.segments.store', [
                'editorial' => $this->editorial->getPublicKey(),
                'asset' => $this->asset->slug,
            ]), [
                'segment' => 'test',
                'language' => 'fr-FR', // French not created
                'data' => ['title' => 'Test'],
            ]);

        $response->assertNotFound();
    }
}